{"version": 3, "file": "URLSearchParamsImpl.js", "sourceRoot": "", "sources": ["../src/URLSearchParamsImpl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,uCAAkD;AAClD,+CAA6E;AAE7E;;GAEG;AACH;IAKE;;;;OAIG;IACH,6BAAY,IAA0D;;QAA1D,qBAAA,EAAA,SAA0D;QARtE,UAAK,GAAuB,EAAE,CAAA;QAC9B,eAAU,GAAe,IAAI,CAAA;QAQ3B;;;;;;;;;;;;WAYG;QACH,IAAI,cAAO,CAAC,IAAI,CAAC,EAAE;;gBACjB,KAAmB,IAAA,SAAA,SAAA,IAAI,CAAA,0BAAA,4CAAE;oBAApB,IAAM,IAAI,iBAAA;oBACb,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBACrB,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAA;qBAC9D;oBACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;iBACpC;;;;;;;;;SACF;aAAM,IAAI,eAAQ,CAAC,IAAI,CAAC,EAAE;YACzB,KAAK,IAAM,IAAI,IAAI,IAAI,EAAE;gBACvB,0BAA0B;gBAC1B,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;iBACpC;aACF;SACF;aAAM;YACL,IAAI,CAAC,KAAK,GAAG,qCAAsB,CAAC,IAAI,CAAC,CAAA;SAC1C;IACH,CAAC;IAED;;OAEG;IACH,0CAAY,GAAZ;QACE;;;;WAIG;QACH,IAAI,KAAK,GAAkB,mCAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC3D,IAAI,KAAK,KAAK,EAAE;YAAE,KAAK,GAAG,IAAI,CAAA;QAC9B,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI;YAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;IAClE,CAAC;IAED,kBAAkB;IAClB,oCAAM,GAAN,UAAO,IAAY,EAAE,KAAa;QAChC;;;;WAIG;QACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;QAC9B,IAAI,CAAC,YAAY,EAAE,CAAA;IACrB,CAAC;IAED,kBAAkB;IAClB,oCAAM,GAAN,UAAO,IAAY;QACjB;;;WAGG;QACH,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9C,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI;gBAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;SACvD;QACD,IAAI,CAAC,YAAY,EAAE,CAAA;IACrB,CAAC;IAED,kBAAkB;IAClB,iCAAG,GAAH,UAAI,IAAY;;;YACd;;;;eAIG;YACH,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,KAAK,CAAA,gBAAA,4BAAE;gBAA1B,IAAM,IAAI,WAAA;gBACb,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI;oBAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;aACrC;;;;;;;;;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,oCAAM,GAAN,UAAO,IAAY;;QACjB;;;;WAIG;QACH,IAAM,MAAM,GAAa,EAAE,CAAA;;YAC3B,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,KAAK,CAAA,gBAAA,4BAAE;gBAA1B,IAAM,IAAI,WAAA;gBACb,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI;oBAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;aAC3C;;;;;;;;;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED,kBAAkB;IAClB,iCAAG,GAAH,UAAI,IAAY;;;YACd;;;eAGG;YACH,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,KAAK,CAAA,gBAAA,4BAAE;gBAA1B,IAAM,IAAI,WAAA;gBACb,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI;oBAAE,OAAO,IAAI,CAAA;aAClC;;;;;;;;;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,kBAAkB;IAClB,iCAAG,GAAH,UAAI,IAAY,EAAE,KAAa;;QAC7B;;;;;;;WAOG;QACH,IAAM,QAAQ,GAAa,EAAE,CAAA;QAC7B,IAAI,KAAK,GAAG,KAAK,CAAA;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;gBAC7B,IAAI,CAAC,KAAK,EAAE;oBACV,KAAK,GAAG,IAAI,CAAA;oBACZ,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAA;iBACzB;qBAAM;oBACL,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;iBACjB;aACF;SACF;QACD,IAAI,CAAC,KAAK,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;SAC/B;;YACD,KAAgB,IAAA,aAAA,SAAA,QAAQ,CAAA,kCAAA,wDAAE;gBAArB,IAAM,CAAC,qBAAA;gBACV,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;aACxB;;;;;;;;;IACH,CAAC;IAED,kBAAkB;IAClB,kCAAI,GAAJ;QACE;;;;;WAKG;QACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAxC,CAAwC,CAAC,CAAA;QACnE,IAAI,CAAC,YAAY,EAAE,CAAA;IACrB,CAAC;IAED,kBAAkB;IACjB,8BAAC,MAAM,CAAC,QAAQ,CAAC,GAAlB;;;;;;;oBAKqB,KAAA,SAAA,IAAI,CAAC,KAAK,CAAA;;;;oBAAlB,IAAI;oBACb,qBAAM,IAAI,EAAA;;oBAAV,SAAU,CAAA;;;;;;;;;;;;;;;;;;;KAEb;IAED,kBAAkB;IAClB,sCAAQ,GAAR;QACE,OAAO,mCAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACzC,CAAC;IAEH,0BAAC;AAAD,CAAC,AAjLD,IAiLC;AAjLY,kDAAmB"}