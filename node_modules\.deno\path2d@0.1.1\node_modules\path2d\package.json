{"name": "path2d", "version": "0.1.1", "description": "Path2D API for node. Can be used for server-side rendering with canvas", "keywords": ["Path2D", "polyfill", "canvas", "roundRect"], "homepage": "https://github.com/nilzona/path2d-polyfill#readme", "bugs": {"url": "https://github.com/nilzona/path2d-polyfill/issues"}, "repository": {"type": "git", "url": "git+https://github.com/nilzona/path2d-polyfill.git"}, "license": "MIT", "author": "nilzona", "type": "module", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "files": ["dist"], "prettier": "@qlik/prettier-config", "devDependencies": {"@qlik/eslint-config": "0.7.13", "@qlik/prettier-config": "0.4.11", "@qlik/tsconfig": "0.2.4", "@swc/core": "1.4.8", "@types/node": "20.11.28", "@vitest/coverage-v8": "1.4.0", "eslint": "8.57.0", "prettier": "3.2.5", "rimraf": "5.0.5", "tsup": "8.0.2", "typescript": "5.4.2", "vitest": "1.4.0"}, "engines": {"node": ">=6"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsup-node src/index.ts --target node18 --format esm,cjs --dts", "check-types": "tsc --noEmit", "format:check": "prettier --check '**' --ignore-unknown", "format:write": "prettier --write '**' --ignore-unknown", "lint": "eslint .", "test": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest", "watch": "pnpm build --watch"}}