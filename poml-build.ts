#!/usr/bin/env -S deno run --allow-read --allow-write

import { parse } from "https://deno.land/std@0.203.0/flags/mod.ts";
import * as path from "https://deno.land/std@0.203.0/path/mod.ts";

// Function to patch pomljs reactRender.js for Deno compatibility
async function patchPomljs() {
  const reactRenderPath = path.join(
    "node_modules", ".deno", "pomljs@0.0.8", "node_modules", "pomljs", "dist", "util", "reactRender.js"
  );

  try {
    const exists = await Deno.stat(reactRenderPath).then(() => true).catch(() => false);
    if (!exists) return;

    const content = await Deno.readTextFile(reactRenderPath);

    // Check if already patched
    if (content.includes("// Patched version of reactRender.js for Deno compatibility")) {
      return;
    }

    // Apply patch
    const patchedContent = `// Patched version of reactRender.js for Deno compatibility
import { renderToString } from 'react-dom/server';

const reactRender = async (element, shellOnly) => {
    try {
        // For Deno, we'll use renderToString as a fallback since renderToPipeableStream is not available
        // This is simpler but less performant than streaming
        const html = renderToString(element);
        return html;
    } catch (error) {
        console.error('Error rendering React element:', error);
        throw error;
    }
};

export { reactRender };
//# sourceMappingURL=reactRender.js.map`;

    await Deno.writeTextFile(reactRenderPath, patchedContent);
    console.log("Applied Deno compatibility patch to pomljs");
  } catch (error) {
    // Silently continue if patching fails
    console.warn("Warning: Could not apply pomljs patch, but continuing anyway");
  }
}

// Import pomljs with error handling
let read, write;
try {
  await patchPomljs();
  const pomljs = await import("npm:pomljs");
  read = pomljs.read;
  write = pomljs.write;
} catch (error) {
  console.error("Failed to import pomljs:", error.message);
  console.error("Please ensure React dependencies are properly configured in deno.json");
  Deno.exit(1);
}

// Function to convert Markdown to HTML
function convertMarkdownToHtml(markdown: string, title: string): string {
  // Simple Markdown to HTML conversion
  let html = markdown
    // Headers
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    // Bold
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Italic
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // Code blocks
    .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
    // Inline code
    .replace(/`(.*?)`/g, '<code>$1</code>')
    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
    // Line breaks
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br>');

  // Wrap in paragraphs
  html = '<p>' + html + '</p>';

  // Fix empty paragraphs
  html = html.replace(/<p><\/p>/g, '');
  html = html.replace(/<p><h([1-6])>/g, '<h$1>');
  html = html.replace(/<\/h([1-6])><\/p>/g, '</h$1>');
  html = html.replace(/<p><pre>/g, '<pre>');
  html = html.replace(/<\/pre><\/p>/g, '</pre>');

  // Create full HTML document
  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 { color: #2c3e50; }
        code {
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        pre code {
            background-color: transparent;
            padding: 0;
        }
        a { color: #3498db; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    ${html}
</body>
</html>`;
}

// Simple CLI args parser (replacing commander)
const args = parse(Deno.args, {
  alias: { v: "version", h: "help" },
  boolean: ["version", "help"],
});

if (args.help || args._.length === 0) {
  console.log(`
Usage: poml-build <inputFile>

Options:
  -h, --help     Show this help message
  -v, --version  Show version
`);
  Deno.exit(0);
}

if (args.version) {
  console.log("poml-build 1.1.0");
  Deno.exit(0);
}

const inputFile = String(args._[0]);

try {
  // Read input POML
  const pomlContent = await Deno.readTextFile(inputFile);

  // Parse into IR
  const ir = await read(pomlContent);

  // Convert to Markdown
  const markdown = write(ir);

  // Resolve repo root (one up from script directory)
//   const repoRoot = path.resolve(path.dirname(path.fromFileUrl(import.meta.url)), ".."); // orginal code
  const repoRoot = path.resolve(path.dirname(path.fromFileUrl(import.meta.url)), ".");
  const generatedRoot = path.join(repoRoot, "generated");

  // Derive domain + name
  const absInput = path.resolve(inputFile);
  const domain = path.basename(path.dirname(absInput));
  const name = path.basename(absInput, ".poml");

  // Convert Markdown to HTML
  const html = convertMarkdownToHtml(markdown, name);

  // Final output dir
  const outputDir = path.join(generatedRoot, domain);
  await Deno.mkdir(outputDir, { recursive: true });

  const outputMd = path.join(outputDir, `${name}.md`);
  const outputJson = path.join(outputDir, `${name}.json`);
  const outputHtml = path.join(outputDir, `${name}.html`);

  // Write outputs
  await Deno.writeTextFile(outputMd, markdown);
  await Deno.writeTextFile(outputJson, JSON.stringify(ir, null, 2));
  await Deno.writeTextFile(outputHtml, html);

  console.log(`Generated: ${outputMd}`);
  console.log(`Generated: ${outputJson}`);
  console.log(`Generated: ${outputHtml}`);
  Deno.exit(0);
} catch (err) {
  console.error("Error:", (err as Error).message);
  Deno.exit(1);
}
