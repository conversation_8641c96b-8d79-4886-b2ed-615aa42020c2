#!/usr/bin/env -S deno run --allow-read --allow-write

import { parse } from "https://deno.land/std@0.203.0/flags/mod.ts";
import * as path from "https://deno.land/std@0.203.0/path/mod.ts";
import { read, write } from "npm:pomljs";

// Simple CLI args parser (replacing commander)
const args = parse(Deno.args, {
  alias: { v: "version", h: "help" },
  boolean: ["version", "help"],
});

if (args.help || args._.length === 0) {
  console.log(`
Usage: poml-build <inputFile>

Options:
  -h, --help     Show this help message
  -v, --version  Show version
`);
  Deno.exit(0);
}

if (args.version) {
  console.log("poml-build 1.1.0");
  Deno.exit(0);
}

const inputFile = String(args._[0]);

try {
  // Read input POML
  const pomlContent = await Deno.readTextFile(inputFile);

  // Parse into IR
  const ir = await read(pomlContent);

  // Convert to Markdown
  const markdown = write(ir);

  // Resolve repo root (one up from script directory)
  const repoRoot = path.resolve(path.dirname(path.fromFileUrl(import.meta.url)), ".");
  const generatedRoot = path.join(repoRoot, "generated");

  // Derive domain + name
  const absInput = path.resolve(inputFile);
  const domain = path.basename(path.dirname(absInput));
  const name = path.basename(absInput, ".poml");

  // Final output dir
  const outputDir = path.join(generatedRoot, domain);
  await Deno.mkdir(outputDir, { recursive: true });

  const outputMd = path.join(outputDir, `${name}.md`);
  const outputJson = path.join(outputDir, `${name}.json`);

  // Write outputs
  await Deno.writeTextFile(outputMd, markdown);
  await Deno.writeTextFile(outputJson, JSON.stringify(ir, null, 2));

  console.log(`Generated: ${outputMd}`);
  console.log(`Generated: ${outputJson}`);
} catch (err) {
  console.error("Error:", (err as Error).message);
  Deno.exit(1);
}
