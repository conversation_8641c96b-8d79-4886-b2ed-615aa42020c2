{"version": 3, "file": "URLImpl.js", "sourceRoot": "", "sources": ["../src/URLImpl.ts"], "names": [], "mappings": ";;AAAA,6DAA2D;AAC3D,2CAA2E;AAC3E,+CAIuB;AAEvB;;GAEG;AACH;IAKE;;;;;OAKG;IACH,iBAAY,GAAW,EAAE,OAAgB;QACvC;;;;;WAKG;QACH,IAAI,UAAU,GAAqB,IAAI,CAAA;QACvC,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,UAAU,GAAG,6BAAc,CAAC,OAAO,CAAC,CAAA;YACpC,IAAI,UAAU,KAAK,IAAI,EAAE;gBACvB,MAAM,IAAI,SAAS,CAAC,wBAAsB,OAAO,OAAI,CAAC,CAAA;aACvD;SACF;QAED;;;;WAIG;QACH,IAAM,SAAS,GAAG,6BAAc,CAAC,GAAG,EAAE,UAAU,CAAC,CAAA;QACjD,IAAI,SAAS,KAAK,IAAI,EAAE;YACtB,MAAM,IAAI,SAAS,CAAC,mBAAiB,GAAG,OAAI,CAAC,CAAA;SAC9C;QAED;;;;;;;;WAQG;QACH,IAAM,KAAK,GAAG,SAAS,CAAC,KAAK,IAAI,EAAE,CAAA;QACnC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QACrB,IAAI,CAAC,YAAY,GAAG,IAAI,yCAAmB,CAAC,KAAK,CAAC,CAAA;QAClD,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAA;IACrC,CAAC;IAGD,sBAAI,yBAAI;QADR,kBAAkB;aAClB;YACE;;;eAGG;YACH,OAAO,4BAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACjC,CAAC;aACD,UAAS,KAAa;YACpB;;;;eAIG;YACH,IAAM,SAAS,GAAG,6BAAc,CAAC,KAAK,CAAC,CAAA;YACvC,IAAI,SAAS,KAAK,IAAI,EAAE;gBACtB,MAAM,IAAI,SAAS,CAAC,mBAAiB,KAAK,OAAI,CAAC,CAAA;aAChD;YACD;;;;;;eAMG;YACH,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;YACrB,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,EAAE,CAAA;YAC5B,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;YAC7B,IAAI,KAAK,KAAK,IAAI,EAAE;gBAClB,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,qCAAsB,CAAC,KAAK,CAAC,CAAA;aACxD;QACH,CAAC;;;OAxBA;IA2BD,sBAAI,2BAAM;QADV,kBAAkB;aAClB;YACE;;;eAGG;YACH,OAAO,2CAA4B,CAAC,qBAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QACxD,CAAC;;;OAAA;IAGD,sBAAI,6BAAQ;QADZ,kBAAkB;aAClB;YACE;;;eAGG;YACH,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAA;QAC/B,CAAC;aACD,UAAa,GAAW;YACtB;;;;eAIG;YACH,6BAAc,CAAC,GAAG,GAAG,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EACvD,wBAAW,CAAC,WAAW,CAAC,CAAA;QAC5B,CAAC;;;OATA;IAYD,sBAAI,6BAAQ;QADZ,kBAAkB;aAClB;YACE;;;eAGG;YACH,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;QAC3B,CAAC;aACD,UAAa,GAAW;YACtB;;;;eAIG;YACH,IAAI,8CAA+B,CAAC,IAAI,CAAC,IAAI,CAAC;gBAAE,OAAM;YACtD,6BAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QAChC,CAAC;;;OATA;IAYD,sBAAI,6BAAQ;QADZ,kBAAkB;aAClB;YACE;;;eAGG;YACH,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;QAC3B,CAAC;aACD,UAAa,GAAW;YACtB;;;;eAIG;YACH,IAAI,8CAA+B,CAAC,IAAI,CAAC,IAAI,CAAC;gBAAE,OAAM;YACtD,6BAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QAChC,CAAC;;;OATA;IAYD,sBAAI,yBAAI;QADR,kBAAkB;aAClB;YACE;;;;;;eAMG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;gBAC3B,OAAO,EAAE,CAAA;aACV;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;gBAClC,OAAO,6BAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;aACtC;iBAAM;gBACL,OAAO,6BAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA;aACxE;QACH,CAAC;aACD,UAAS,GAAW;YAClB;;;;;eAKG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,qBAAqB;gBAAE,OAAM;YAC3C,6BAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EACjD,wBAAW,CAAC,IAAI,CAAC,CAAA;QACrB,CAAC;;;OAXA;IAcD,sBAAI,6BAAQ;QADZ,kBAAkB;aAClB;YACE;;;eAGG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI;gBAAE,OAAO,EAAE,CAAA;YACtC,OAAO,6BAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACvC,CAAC;aACD,UAAa,GAAW;YACtB;;;;;eAKG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,qBAAqB;gBAAE,OAAM;YAC3C,6BAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EACjD,wBAAW,CAAC,QAAQ,CAAC,CAAA;QACzB,CAAC;;;OAXA;IAcD,sBAAI,yBAAI;QADR,kBAAkB;aAClB;YACE;;;eAGG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI;gBAAE,OAAO,EAAE,CAAA;YACtC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA;QAClC,CAAC;aACD,UAAS,GAAW;YAClB;;;;;;;eAOG;YACH,IAAI,8CAA+B,CAAC,IAAI,CAAC,IAAI,CAAC;gBAAE,OAAM;YACtD,IAAI,GAAG,KAAK,EAAE,EAAE;gBACd,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;aACtB;iBAAM;gBACL,6BAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EACjD,wBAAW,CAAC,IAAI,CAAC,CAAA;aACpB;QACH,CAAC;;;OAjBA;IAoBD,sBAAI,6BAAQ;QADZ,kBAAkB;aAClB;YACE;;;;;;;eAOG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,qBAAqB;gBAAE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YAC7D,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,EAAE,CAAA;YAC1C,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACvC,CAAC;aACD,UAAa,GAAW;YACtB;;;;;eAKG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,qBAAqB;gBAAE,OAAM;YAC3C,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;YACnB,6BAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EACjD,wBAAW,CAAC,SAAS,CAAC,CAAA;QAC1B,CAAC;;;OAZA;IAeD,sBAAI,2BAAM;QADV,kBAAkB;aAClB;YACE;;;;eAIG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;gBAAE,OAAO,EAAE,CAAA;YACjE,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAA;QAC9B,CAAC;aACD,UAAW,GAAW;YACpB;;;;;;;;;;;eAWG;YACH,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAA;YACrB,IAAI,GAAG,KAAK,EAAE,EAAE;gBACd,GAAG,CAAC,KAAK,GAAG,IAAI,CAAA;gBAChB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;gBAClC,OAAM;aACP;YACD,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC;gBAAE,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YAC5C,GAAG,CAAC,KAAK,GAAG,EAAE,CAAA;YACd,6BAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,wBAAW,CAAC,KAAK,CAAC,CAAA;YACjE,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,qCAAsB,CAAC,GAAG,CAAC,CAAA;QACvD,CAAC;;;OAxBA;IA2BD,sBAAI,iCAAY;QADhB,kBAAkB;aAClB,cAAsC,OAAO,IAAI,CAAC,YAAY,CAAA,CAAC,CAAC;;;OAAA;IAGhE,sBAAI,yBAAI;QADR,kBAAkB;aAClB;YACE;;;;eAIG;YACH,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,EAAE;gBAAE,OAAO,EAAE,CAAA;YACvE,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;QACjC,CAAC;aACD,UAAS,GAAW;YAClB;;;;;;;;eAQG;YACH,IAAI,GAAG,KAAK,EAAE,EAAE;gBACd,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACzB,OAAM;aACP;YACD,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC;gBAAE,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YAC5C,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAA;YACvB,6BAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EACjD,wBAAW,CAAC,QAAQ,CAAC,CAAA;QACzB,CAAC;;;OAnBA;IAsBD,kBAAkB;IAClB,wBAAM,GAAN,cAAmB,OAAO,4BAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAC,CAAC;IAEpD,kBAAkB;IAClB,0BAAQ,GAAR;QACE,OAAO,IAAI,CAAC,IAAI,CAAA;IAClB,CAAC;IAEH,cAAC;AAAD,CAAC,AAxUD,IAwUC;AAxUY,0BAAO"}