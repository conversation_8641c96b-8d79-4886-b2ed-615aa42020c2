{"version": 3, "file": "subselects.d.ts", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["pseudo-selectors/subselects.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAC;AAEzC,OAAO,KAAK,EACR,aAAa,EACb,eAAe,EACf,YAAY,EACZ,OAAO,EACV,MAAM,aAAa,CAAC;AAGrB,gFAAgF;AAChF,eAAO,MAAM,mBAAmB,IAAK,CAAC;AAEtC,wBAAgB,WAAW,CAAC,IAAI,EAAE,WAAW,SAAS,IAAI,EACtD,IAAI,EAAE,aAAa,CAAC,WAAW,CAAC,EAChC,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,GACpC,aAAa,CAAC,IAAI,CAAC,CAGrB;AAED,oBAAY,SAAS,GAAG,CAAC,IAAI,EAAE,WAAW,SAAS,IAAI,EACnD,IAAI,EAAE,aAAa,CAAC,WAAW,CAAC,EAChC,SAAS,EAAE,QAAQ,EAAE,EAAE,EACvB,OAAO,EAAE,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,EAC3C,OAAO,EAAE,IAAI,EAAE,GAAG,SAAS,EAC3B,YAAY,EAAE,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,KAC5C,aAAa,CAAC,WAAW,CAAC,CAAC;AAEhC,wBAAgB,eAAe,CAAC,IAAI,EAAE,WAAW,SAAS,IAAI,EAC1D,IAAI,EAAE,IAAI,EACV,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,GACpC,WAAW,EAAE,CAMf;AAiCD,eAAO,MAAM,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAgEhD,CAAC"}