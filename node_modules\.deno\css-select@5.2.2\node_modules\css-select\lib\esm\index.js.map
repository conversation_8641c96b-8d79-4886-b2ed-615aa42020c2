{"version": 3, "file": "index.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["index.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,QAAQ,MAAM,UAAU,CAAC;AACrC,OAAO,QAAQ,MAAM,UAAU,CAAC;AAKhC,OAAO,EACH,OAAO,IAAI,UAAU,EACrB,aAAa,EACb,YAAY,GACf,MAAM,cAAc,CAAC;AAStB,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AAInE,MAAM,aAAa,GAAG,CAAO,CAAO,EAAE,CAAO,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AAC1D,MAAM,cAAc,GAAuD;IACvE,OAAO,EAAE,QAAQ;IACjB,MAAM,EAAE,aAAa;CACxB,CAAC;AAEF,SAAS,oBAAoB,CACzB,OAAoC;;IAEpC;;OAEG;IACH,iFAAiF;IACjF,MAAM,IAAI,GAA+B,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,cAAc,CAAC;IACnE,kCAAkC;IAClC,MAAA,IAAI,CAAC,OAAO,oCAAZ,IAAI,CAAC,OAAO,GAAK,QAAQ,EAAC;IAC1B,wDAAwD;IACxD,MAAA,IAAI,CAAC,MAAM,oCAAX,IAAI,CAAC,MAAM,GAAK,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,mCAAI,aAAa,EAAC;IAEtD,OAAO,IAA0C,CAAC;AACtD,CAAC;AAED,SAAS,WAAW,CAChB,IAIqB;IAErB,OAAO,SAAS,UAAU,CACtB,QAAkB,EAClB,OAAoC,EACpC,OAAuB;QAEvB,MAAM,IAAI,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAE3C,OAAO,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC,CAAC;AACN,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC;AAC/C,MAAM,CAAC,MAAM,cAAc,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC;AACzD,MAAM,CAAC,MAAM,aAAa,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC;AAEvD,SAAS,eAAe,CACpB,UAIM;IAEN,OAAO,SAAS,MAAM,CAClB,KAAyB,EACzB,QAAuB,EACvB,OAAoC;QAEpC,MAAM,IAAI,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAE3C,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC7B,KAAK,GAAG,aAAa,CAAoB,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;SACnE;QAED,MAAM,gBAAgB,GAAG,cAAc,CACnC,QAAQ,EACR,IAAI,CAAC,OAAO,EACZ,KAAK,CAAC,sBAAsB,CAC/B,CAAC;QACF,OAAO,UAAU,CAAC,KAAK,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;IACrD,CAAC,CAAC;AACN,CAAC;AAED,MAAM,UAAU,cAAc,CAC1B,KAAoB,EACpB,OAAmC,EACnC,sBAAsB,GAAG,KAAK;IAE9B;;;OAGG;IACH,IAAI,sBAAsB,EAAE;QACxB,KAAK,GAAG,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;KAC9C;IAED,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACvB,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC;QAC9B,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,kBAAkB,CACvB,IAAmB,EACnB,OAAmC;IAEnC,+EAA+E;IAC/E,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3D,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;IAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;QAClC,MAAM,YAAY,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QACxD,KAAK,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;KAC/B;IACD,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,eAAe,CACpC,CACI,KAA6B,EAC7B,KAAoB,EACpB,OAA2C,EAC9B,EAAE,CACf,KAAK,KAAK,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;IACxD,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAClD,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,eAAe,CACpC,CACI,KAA6B,EAC7B,KAAoB,EACpB,OAA2C,EACzB,EAAE,CACpB,KAAK,KAAK,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;IACxD,CAAC,CAAC,IAAI;IACN,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAClD,CAAC;AAEF;;;;;;;;;;GAUG;AACH,MAAM,UAAU,EAAE,CACd,IAAiB,EACjB,KAAyB,EACzB,OAAoC;IAEpC,MAAM,IAAI,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAC3C,OAAO,CAAC,OAAO,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAClE,IAAI,CACP,CAAC;AACN,CAAC;AAED;;;GAGG;AACH,eAAe,SAAS,CAAC;AAEzB,0EAA0E;AAC1E,oDAAoD;AACpD,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,6BAA6B,CAAC"}