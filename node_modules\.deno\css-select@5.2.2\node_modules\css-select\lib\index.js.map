{"version": 3, "file": "index.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/css-select/93caad96c807da1d48f08166ef14cf26916b9364/src/", "sources": ["index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iDAAqC;AACrC,sDAAgC;AAKhC,2CAIsB;AAStB,kEAAmE;AAInE,IAAM,aAAa,GAAG,UAAO,CAAO,EAAE,CAAO,IAAK,OAAA,CAAC,KAAK,CAAC,EAAP,CAAO,CAAC;AAC1D,IAAM,cAAc,GAAuD;IACvE,OAAO,EAAE,QAAQ;IACjB,MAAM,EAAE,aAAa;CACxB,CAAC;AAEF,SAAS,oBAAoB,CACzB,OAAoC;;IAEpC;;OAEG;IACH,iFAAiF;IACjF,IAAM,IAAI,GAA+B,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,cAAc,CAAC;IACnE,kCAAkC;IAClC,MAAA,IAAI,CAAC,OAAO,oCAAZ,IAAI,CAAC,OAAO,GAAK,QAAQ,EAAC;IAC1B,wDAAwD;IACxD,MAAA,IAAI,CAAC,MAAM,oCAAX,IAAI,CAAC,MAAM,GAAK,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,MAAM,mCAAI,aAAa,EAAC;IAEtD,OAAO,IAA0C,CAAC;AACtD,CAAC;AAED,SAAS,WAAW,CAChB,IAIqB;IAErB,OAAO,SAAS,UAAU,CACtB,QAAkB,EAClB,OAAoC,EACpC,OAAuB;QAEvB,IAAM,IAAI,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAE3C,OAAO,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACzC,CAAC,CAAC;AACN,CAAC;AAED;;GAEG;AACU,QAAA,OAAO,GAAG,WAAW,CAAC,oBAAU,CAAC,CAAC;AAClC,QAAA,cAAc,GAAG,WAAW,CAAC,0BAAa,CAAC,CAAC;AAC5C,QAAA,aAAa,GAAG,WAAW,CAAC,yBAAY,CAAC,CAAC;AAEvD,SAAS,eAAe,CACpB,UAIM;IAEN,OAAO,SAAS,MAAM,CAClB,KAAyB,EACzB,QAAuB,EACvB,OAAoC;QAEpC,IAAM,IAAI,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAE3C,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC7B,KAAK,GAAG,IAAA,0BAAa,EAAoB,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;SACnE;QAED,IAAM,gBAAgB,GAAG,cAAc,CACnC,QAAQ,EACR,IAAI,CAAC,OAAO,EACZ,KAAK,CAAC,sBAAsB,CAC/B,CAAC;QACF,OAAO,UAAU,CAAC,KAAK,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;IACrD,CAAC,CAAC;AACN,CAAC;AAED,SAAgB,cAAc,CAC1B,KAAoB,EACpB,OAAmC,EACnC,sBAA8B;IAA9B,uCAAA,EAAA,8BAA8B;IAE9B;;;OAGG;IACH,IAAI,sBAAsB,EAAE;QACxB,KAAK,GAAG,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;KAC9C;IAED,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACvB,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC;QAC9B,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACrC,CAAC;AAhBD,wCAgBC;AAED,SAAS,kBAAkB,CACvB,IAAmB,EACnB,OAAmC;IAEnC,+EAA+E;IAC/E,IAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3D,IAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;IAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;QAClC,IAAM,YAAY,GAAG,IAAA,+BAAe,EAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QACxD,KAAK,CAAC,IAAI,OAAV,KAAK,EAAS,YAAY,EAAE;KAC/B;IACD,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;;;;;GASG;AACU,QAAA,SAAS,GAAG,eAAe,CACpC,UACI,KAA6B,EAC7B,KAAoB,EACpB,OAA2C;IAE3C,OAAA,KAAK,KAAK,kBAAQ,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QACxD,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;AAF3C,CAE2C,CAClD,CAAC;AAEF;;;;;;;;GAQG;AACU,QAAA,SAAS,GAAG,eAAe,CACpC,UACI,KAA6B,EAC7B,KAAoB,EACpB,OAA2C;IAE3C,OAAA,KAAK,KAAK,kBAAQ,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;QACxD,CAAC,CAAC,IAAI;QACN,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;AAF3C,CAE2C,CAClD,CAAC;AAEF;;;;;;;;;;GAUG;AACH,SAAgB,EAAE,CACd,IAAiB,EACjB,KAAyB,EACzB,OAAoC;IAEpC,IAAM,IAAI,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAC3C,OAAO,CAAC,OAAO,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAA,oBAAU,EAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAClE,IAAI,CACP,CAAC;AACN,CAAC;AATD,gBASC;AAED;;;GAGG;AACH,kBAAe,iBAAS,CAAC;AAEzB,0EAA0E;AAC1E,oDAAoD;AACpD,wDAAwE;AAA/D,mGAAA,OAAO,OAAA;AAAE,mGAAA,OAAO,OAAA;AAAE,mGAAA,OAAO,OAAA"}