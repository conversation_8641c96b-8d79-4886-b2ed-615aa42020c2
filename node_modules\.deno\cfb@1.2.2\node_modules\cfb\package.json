{"name": "cfb", "version": "1.2.2", "author": "sheetjs", "description": "Compound File Binary File Format extractor", "keywords": ["cfb", "compression", "office"], "main": "./cfb", "types": "types", "browser": {"node": false, "process": false, "fs": false}, "dependencies": {"adler-32": "~1.3.0", "crc-32": "~1.2.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.10.25", "acorn": "7.4.1", "alex": "8.1.1", "blanket": "~1.2.3", "dtslint": "~0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "jscs": "3.0.7", "jshint": "2.13.4", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"pretest": "make init", "test": "make test", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "cfb.js"}}, "files": ["LICENSE", "README.md", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}}